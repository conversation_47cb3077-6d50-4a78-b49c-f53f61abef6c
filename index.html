<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="UTF-8" />
    <link rel=icon href=/favicon.ico>
    <title>闪灵猫-分割</title>
    <link rel="stylesheet" href="./static/css/style.css" />
    <script src="./static/js/vconsole.js"></script>
  <script>
    new VConsole({
      defaultPlugins: ['system']
    });
  </script>
    <style>
      #__vconsole .vc-switch {
        bottom: 100px !important;
      }
      .qrcode-container {
        margin: 20px 0;
        padding: 10px;
        border: 1px solid #eee;
        display: inline-block;
      }

      .qr-image {
        margin-top: 20px;
      }

      .qr-image img {
        display: block;
        margin-bottom: 10px;
      }
    .dialog {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.6);
      z-index: 9999;
    }

    .dialog .dialog-container {
      width: 458px;
      min-height: 240px;
      background: #ffffff;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 8px;
      position: relative;
    }

    .dialog .dialog-container .dialog-title {
      width: 100%;
      height: 60px;
      font-size: 18px;
      color: #696969;
      font-weight: 600;
      padding: 16px 50px 0 20px;
      box-sizing: border-box;
    }

    .dialog .dialog-container .dialog-content {
        max-height: 610px;
    overflow-y: scroll;
      color: #797979;
      line-height: 26px;
      padding: 0 20px;
      box-sizing: border-box;
          padding-bottom: 80px;
          overflow-wrap: break-word;
    }

    .dialog .dialog-container .inp {
      margin: 10px 0 0 20px;
      width: 200px;
      height: 40px;
      padding-left: 4px;
      border-radius: 4px;
      border: none;
      background: #efefef;
      outline: none;
    }

    .dialog .dialog-container .inp:focus {
      border: 1px solid #509ee3;
    }

    .dialog .dialog-container .btns {

      width: 100%;
      height: 90px;
      text-align: right;
      padding: 20px 16px;
      box-sizing: border-box;
      position: absolute;
      bottom: 0;
      left: 0;
    }

    .dialog .dialog-container .btns>div {
      display: inline-block;
      height: 40px;
      line-height: 40px;
      padding: 0 14px;
      color: #ffffff;
      background: #f1f1f1;
      border-radius: 8px;
      margin-right: 12px;
      cursor: pointer;
    }

    .dialog .dialog-container .btns .default-btn {
      color: #787878;
    }

    .dialog .dialog-container .btns .default-btn:hover {
      color: #509ee3;
    }

    .dialog .dialog-container .btns .danger-btn {
      background: #ef8c8c;
    }

    .dialog .dialog-container .btns .danger-btn:hover {
      background: #e08787;
    }

    .dialog .dialog-container .btns .danger-btn:active {
      background: #ef8c8c;
    }

    .dialog .dialog-container .btns .confirm-btn {
      color: #ffffff;
      background: #509ee3;
    }

    .dialog .dialog-container .btns .confirm-btn:hover {
      background: #6fb0eb;
    }

    .dialog .dialog-container .close-btn {
      position: absolute;
      top: 16px;
      right: 16px;
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 18px;
      cursor: pointer;
    }

    .dialog .dialog-container .close-btn:hover {
      font-weight: 600;
    }
    </style>
    <script src="./static/js/vue.js"></script>
    <script src="./static/js/jquery-1.8.3.min.js"></script>
  </head>
  <body>
    <div id="app" v-cloak>
	<div v-if="!isLogin"  style="text-align: center; display: flex; justify-content: center; align-items: center; flex-direction:column; margin-top: 50px; color: #000; font-size: 16px; line-height: 30px">
    
    
   
    <div @click="copy()" class="detail-input-item" style="flex-direction: row;display: flex; width: 100%; align-items: center; justify-content: center;">
      <input class="input" disabled :value="'设备ID：' + deviceId" type="text" style="width: 360px; cursor: pointer;">
    </div>
    <!-- <div class="detail-input-item" style="flex-direction: row;display: flex; width: 100%; align-items: center; justify-content: center;">
      <div style="width: 100px; text-align: right;">授权密钥：</div>
      <input v-model="secret"  class="input" type="text" style="width: 300px;">
    </div> -->
    <div class="qrcode-container">
      <canvas ref="qrCanvas"></canvas>
    </div>
    <div>使用微信扫码充值续费</div>
    <div class="btn-item"  style="width: 360px; margin-top: 20px" @click="checkLogin(true)">
      开始使用
    </div>
    <p>客服微信：dun667788dun</p>
	</div>
	  <div v-else>
      <div class="detail-input">
        
        
          <label for="videos" style="width: 360px">
            <div v-if="!recording" class="btn-item">
              选择视频素材
              <input
                type="file"
                multiple="multiple"
                style="display: none"
                id="videos"
                accept="video/mp4"
                @change="fnSelectVideos()"
              />
            </div>
            <div v-else class="btn-item disabled">
              正在导入视频，请耐心等待...
            </div>
            <div v-if="videoList.length">已导入：{{ videoList.length }}条</div>
          </label>
          <label v-if="videoList.length" style="width: 360px">
            <div class="btn-item" @click="selectDir('selectVideoDir')">
              选择导出文件夹
            </div>
            <div>{{videoPath}}</div>
          </label>

          <label  style="width: 360px">
            <div class="btn-item" @click="start()" >
              开始切分镜头
            </div>
        </label>


      </div>
      <div class="detail-input">
        <div class="detail-input-item">
          分割阈值（范围1-5）
          <input
            min="1"
            max="5"
            class="input"
            type="number"
            placeholder="范围1-5"
            v-model="threshold"
          />
        </div>
        <div class="detail-input-item">
          保留最短镜头时长（秒）
          <input
            class="input"
            type="number"
            placeholder="保留最短镜头时长"
            v-model="minSegmentDuration"
          />
        </div>
        <div class="detail-input-item">
          最终合成镜头时长（秒）
          <input
            class="input"
            type="number"
            placeholder="最终合成镜头时长"
            v-model="minSaveSegmentDuration"
          />
        </div>
        <div class="detail-input-item">
          单个素材最多切分数量
          <input
            class="input"
            type="number"
            placeholder="单个素材最多切分数量"
            v-model="maxSaveCount"
          />
        </div>
        <div class="detail-input-item">
          视频放大比例（范围1-2）
          <input
            min="1"
            max="2"
            class="input"
            type="number"
            placeholder="视频放大比例"
            v-model="cropScale"
          />
        </div>
        <div class="detail-input-item">
          生成视频尺寸
          <select v-model="videoSizeStr" @change="selectVideoSize" class="input">
             <option value="1080:1440">
                1080:1440
            </option>
            <option value="1080:1920">
                1080:1920
            </option>
        </select>
        </div>
        <div class="detail-input-item">
          截取的字幕位置
          <select v-model="isTop" class="input">
             <option :value="true">
                字幕在下方
            </option>
            <option :value="false">
                字幕在上方
            </option>
        </select>
        </div>
        <div class="detail-input-item">
          素材不足时是否合成片段
          <input
            type="checkbox"
            class="input"
            v-model="isMerge"
          />
        </div>
         <div class="detail-input-item" style="width: 400px;">
          是否删除包含人脸素材
          <input
            type="checkbox"
            class="input"
            v-model="deleteFaceVideo"
          />
          <div style="font-size: 13px; color: red">*注意：此功能开启，影响切割速度，建议挂机使用</div>
        </div>
      </div>

      </div>
      
      <div style="position: fixed; display: flex; align-items: center; width: 100%; justify-content: space-between; box-sizing:border-box; padding: 30px; bottom: 0;">
        
        <div v-if="expireTs">到期时间：{{ getTime('Y-m-d', parseInt(expireTs / 1000))}}</div>
        <div> 版本：v{{version}}</div>
     
      </div>
                  <div  v-if="videoList.length > 0" style="text-align: center;margin-top: 30px;padding-bottom: 15px;">裁剪预览：</div>

      <div v-if="videoList.length > 0" style="    display: flex;justify-content: center; ">

        <div  style="position: relative;    display: flex;
    justify-content: center;">
          <video
              :src="videoList[0].path"
              :style="`width: ${height == '1440' ? '400px' : 'auto'}; height: ${height == '1440' ? 'auto' : '540px'}; position: relative;`"
            ></video>
            <div style="position: absolute; border: 4px solid red; width: 100%; height: 100%; z-index: 1;  box-sizing: border-box;" :style="{
              width:  height == 1920 ? (width / height  * (540 / cropScale)) + 'px' : (400 / cropScale) + 'px',
              height: height == 1920 ? (540 / cropScale) + 'px' : (height / width  * (400 / cropScale)) + 'px',
              bottom: !isTop ? 0 : 'unset',
              top: isTop ? 0 : 'unset',
            }"></div>
        </div>
        <!-- <div
          v-for="(item,idx) in fileList"
          class="bg-box"
          :style="`width: ${width}px; height: 280px`"
          style="border: 1px solid #ccc; float: left;"
          v-show="item.percent!=100"
        >
       
          <div :style="boxStyle()" class="img-box" style=" z-index: 3; width: 400px">
            <video
            @loadedmetadata="(event) => onLoadedMetadata(event, item)"
            :src="item.path"
            class="bg-img"
			      :style="`width: 400px; height: position: relative; z-index: 2;`"
          ></video>
          </div>
          <div class="video-percent-mask" v-if="loading">
            <div class="video-percent-text">
              {{percent||'加载中。。。'}}%
            </div>
            <div class="video-percent">
              <div
                class="video-percent-content"
                :style="getWidthStyle(percent)"
              ></div>
            </div>
          </div>
        </div> -->
      </div>
      <div style="height: 50px"></div>
      <div class="dialog" v-show="showSystemMsg">
        <div class="dialog-container" :style="`min-height: 200px`">
          <div class="dialog-title">系统提示</div>
          <div class="dialog-content">
            {{ systemMsg }}
          </div>
          <div v-if="showCloseBtn" class="btns">
            <div class="confirm-btn" @click="() => showSystemMsg = false">
              知道了~
            </div>
          </div>
          <div class="close-btn" @click="() => showSystemMsg = false">
            <i class="iconfont icon-close"></i>
          </div>
        </div>
      </div>
      <div class="dialog" v-show="showVersionMsg">
        <div class="dialog-container" :style="`min-height: 200px`">
          <div class="dialog-title">版本升级提醒</div>
          <div class="dialog-content">
            <div>最新版本：v{{ lineVersion }}</div>
            <div>更新内容：</div>
            <div>{{ versionRemark }}</div>
          </div>
 
          <div  class="btns">
            <div class="danger-btn" @click="() => showVersionMsg = false">
             忽略此版本
            </div>
            <div class="confirm-btn" @click="copy2">
             下载新版本
            </div>
          </div>
          <div class="close-btn" @click="() => showSystemMsg = false">
            <i class="iconfont icon-close"></i>
          </div>
        </div>
      </div>
    </div>
	</div>
    <script>
	  const path = window.require('path');
    const fs = window.require('fs');
    const QRCode = require('qrcode');
    const os = require('os')
    const fontList = window.require('font-list');
    const SubtitlesParser  = require('subtitles-parser');
    const ffmpeg = path.join(__dirname, "../ffmpeg/bin/ffmpeg");
    const modelPath = path.join(__dirname, '../models');
    const util = require('util');
    const exec = window.require("child_process").exec;
    const { shell, ipcRenderer } = window.require("electron");
	  const axios = window.require('axios');
    const faceapi = require('face-api.js');
    const execPromise = util.promisify(exec);
    const canvas = require('canvas')
    let workerProcess;


    // 将时间字符串转换为毫秒
    function timeToMilliseconds(time) {
      // 使用正则表达式提取小时、分钟、秒和毫秒
      const parts = time.match(/(\d+):(\d+):(\d+),(\d+)/);
      if (!parts) throw new Error('Invalid time format');

      const hours = parseInt(parts[1], 10);
      const minutes = parseInt(parts[2], 10);
      const seconds = parseInt(parts[3], 10);
      const milliseconds = parseInt(parts[4], 10);

      // 计算总毫秒数
      return (
        hours * 3600 * 1000 + 
        minutes * 60 * 1000 + 
        seconds * 1000 + 
        milliseconds
      );
    }

    // 获取MAC地址
    function getMAC() {
      const interfaces = os.networkInterfaces()
      for (const name in interfaces) {
        for (const iface of interfaces[name]) {
          if (!iface.internal && iface.mac !== '00:00:00:00:00:00') {
            return iface.mac.replace(/:/g, '')
          }
        }
      }
      return '00'
    }

    // 获取硬盘序列号（多平台兼容）
    function getSerial() {
      return new Promise((resolve) => {
        let cmd = ''
        if (process.platform === 'win32') {
          cmd = 'wmic diskdrive get serialnumber'
        } else if (process.platform === 'darwin') {
          cmd = 'system_profiler SPHardwareDataType | grep "Serial"'
        } else {
          cmd = 'cat /sys/class/dmi/id/product_uuid 2>/dev/null || hostnamectl | grep "Hardware UUID"'
        }

        exec(cmd, (err, stdout) => {
          let serial = ''
          if (!err) {
            // Windows解析
            if (process.platform === 'win32') {
              const match = stdout.match(/^\s*(\S+)\s*$/m)
              if (match) serial = match[1]
            }
            // macOS解析
            else if (process.platform === 'darwin') {
              const match = stdout.match(/Serial Number \(system\): (\S+)/)
              if (match) serial = match[1]
            }
            // Linux解析
            else {
              const match = stdout.match(/(Hardware UUID|product_uuid):?\s*(\S+)/)
              if (match) serial = match[2]
            }
          }
          resolve(serial || 'unknown')
        })
      })
    }

    function customHash(str) {
      let hash = Buffer.alloc(16) // 16字节 = 32位十六进制
      
      // 填充处理
      str = str.length < 16 ? str + str.repeat(16) : str
      const bytes = Buffer.from(str.slice(0, 32)) // 取前32字节
      
      // 自定义处理（示例：循环异或+位移）
      bytes.forEach((b, i) => {
        hash[i % 16] ^= (b << 3) | (b >> 5)
      })
      
      return hash.toString('hex')
    }

    async function generateCustomId() {
      const mac = getMAC()
      const serial = await getSerial()
      return customHash(mac + serial)
    }


      function getTime(format = "YmdHis", time) {
        var d = new Date();
        if (time) {
          d = new Date(parseInt(time) * 1000);
        }

        var year = d.getFullYear();
        var month = d.getMonth() + 1;
        var date = d.getDate();
        var hour = d.getHours();
        var minute = d.getMinutes();
        var second = d.getSeconds();
        return format
          .replace("Y", year)
          .replace("m", month < 10 ? "0" + month : month)
          .replace("d", date < 10 ? "0" + date : date)
          .replace("H", hour < 10 ? "0" + hour : hour)
          .replace("i", minute < 10 ? "0" + minute : minute)
          .replace("s", second < 10 ? "0" + second : second);
      }

      Vue.config.silent = true;
      var vm = new Vue({
        el: "#app",
        data() {
          return {
            recording: false,
            deleteFaceVideo: false,
            isMerge: true,
            isTop: true,
            expireTs: null,
            videoPath: '',
            showCloseBtn: false,
            deviceId: "",
            secret: "",
            threshold: 1,
            minSegmentDuration: 1,
            minSaveSegmentDuration: 3,
            maxSaveCount: 20,
            showSystemMsg: false,
            systemMsg: "",
            videoList: [],
            width: 1080,
            height: 1440,
			      isLogin: false,
            cropScale: 1,
            videoSizeStr: '1080:1440',
            version: '2.0.4',
            showVersionMsg: false,
            lineVersion: '',
            versionRemark: "",
            downloadUrl: ""
          };
        },
        watch: {
          width(v) {
            this.width = parseFloat(v);
          },
          height(v) {
            this.height = parseFloat(v);
          },
        },
        async created() {
          ipcRenderer.on('checkLogin', async (event, result, isClick, errMsg, expireTs) => {
            this.isLogin = result;
            this.expireTs = this.isLogin ? expireTs : null;
            if (!this.isLogin && isClick) {
              this.alert(errMsg)
            }
          })
          ipcRenderer.on('selectVideoDir', async (event, filePath) => {
              if (!filePath) {
                return
              }
              this.videoPath = filePath;
            })
          this.secret = window.localStorage.getItem('secret') || ""
          this.checkLogin().then(() => {
            this.generateQRCode()
          });
          this.getVersion()
          setInterval(() => { this.checkLogin(); }, 5 * 60 * 1000);
          this.faceInit(modelPath)
        },
        methods: {
          copy2() {
              var textArea = document.createElement("textarea");
              textArea.value = this.downloadUrl;
              
              // 避免文本区域的显示
              textArea.style.top = "0";
              textArea.style.left = "0";
              textArea.style.position = "fixed";
          
              document.body.appendChild(textArea);
              textArea.focus();
              textArea.select();
          
              try {
                  var successful = document.execCommand('copy');
                  alert('下载链接已复制，请打开浏览器下载！')
              } catch (err) {
                  alert('复制下载链接失败：' + err.message)
              }
          
              document.body.removeChild(textArea);
          },
          getVersion() {
            axios
              .get(
                "http://api.wang.novel.meeluo.com/api/admin/setting/soft_version"
              )
              .then((response) => {
                if (response.data.code == 0) {
                  const data = response.data.data;
                  if (data.version && data.version !== this.version) {
                    this.lineVersion = data.version;
                    this.versionRemark = data.remark;
                    this.downloadUrl = data.download_url;
                    this.showVersionMsg = true;
                  }
                }
            })
          },
          selectVideoSize(val) {
            const arr = val.target.value.split(':')
            this.width = parseInt(arr[0])
            this.height = parseInt(arr[1])
          },
          selectIsTop(val) {
            this.isTop = val.target.value === 'true';
          },
          async getVideoDimensions(filePath) {
            return new Promise((resolve, reject) => {
              const ffprobePath = path.join(path.dirname(ffmpeg), 'ffprobe');
              exec(`"${ffprobePath}" -v error -select_streams v:0 -show_entries stream=width,height -of json "${filePath}"`,
                (error, stdout, stderr) => {
                  if (error) {
                    reject(error);
                    return;
                  }
                  try {
                    const result = JSON.parse(stdout);
                    if (result.streams && result.streams[0]) {
                      resolve({
                        width: result.streams[0].width,
                        height: result.streams[0].height
                      });
                    } else {
                      reject(new Error('无法获取视频尺寸'));
                    }
                  } catch (e) {
                    reject(e);
                  }
                }
              );
            });
          },
          // 获取视频总时长
          async getVideoDuration(sucaiPath) {
            const ffprobePath = path.join(path.dirname(ffmpeg), 'ffprobe');
            const command = `"${ffprobePath}" -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${sucaiPath}"`;
            
            return new Promise((resolve, reject) => {
              exec(command, (error, stdout, stderr) => {
                if (error) {
                  return reject(new Error(`获取视频时长失败: ${error.message}`));
                }
                
                const duration = parseFloat(stdout.trim());
                if (isNaN(duration)) {
                  return reject(new Error('无法解析视频时长'));
                }
                
                resolve(duration);
              });
            });
          },
          // 解析场景变化时间点
          parseSceneChangeTimePoints(output, duration) {
            const timePoints = [];
            const regex = /pts_time:(\d+\.\d+)/g;
            
            let match;
            while ((match = regex.exec(output)) !== null) {
              const timePoint = parseFloat(match[1]);
              timePoints.push(timePoint);
            }
            
            if (timePoints.length === 0) {
              if (duration < 3) {
                return timePoints
              }
              return [duration - 3];
            }
            
            // 添加开始时间点（0秒）
            if (timePoints[0] !== 0) {
              timePoints.unshift(0);
            }
            
            console.log(`成功检测到 ${timePoints.length - 1} 个场景变化`);
            return timePoints;
          },
          async detectSceneChanges(sucaiPath, duration) {
            const command = `"${ffmpeg}" -i "${sucaiPath}" -filter_complex "select='gt(scene,${this.threshold / 10})',showinfo" -f null -`;
            console.log(command)
            return new Promise((resolve, reject) => {
              const ffmpegProcess = exec(command, { maxBuffer: 1024 * 1024 * 500 }, (error, stdout, stderr) => {
                // 捕获标准错误输出，因为FFmpeg的日志信息通常输出到stderr
                const output = stderr;
                console.log(output);
                // 检查命令执行是否出错
                if (error) {
                  // 检查是否是预期的FFmpeg错误（以"At least one output file must be specified"结尾）
                  if (output.includes('At least one output file must be specified')) {
                    console.log('FFmpeg命令正常执行，正在解析场景变化时间点...');
                  } else {
                    return reject(new Error(`FFmpeg执行失败: ${error.message}`));
                  }
                }
                
                try {
                  // 解析场景变化时间点
                  const timePoints = this.parseSceneChangeTimePoints(output, duration);
                  resolve(timePoints);
                } catch (parseError) {
                  reject(new Error(`解析场景变化时间点失败: ${parseError.message}`));
                }
              });
              
              // 监听进程错误
              ffmpegProcess.on('error', (err) => {
                reject(new Error(`启动FFmpeg进程失败: ${err.message}`));
              });

            });
          },
          async extractFrames(videoPath, outputDir, fps = 2, duration) {
            // 确保输出目录存在
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }
            console.log(duration)
            const seconds = parseInt(duration / 2)
            const date = new Date(0);
            date.setSeconds(seconds); // 设置秒数
            // 生成输出文件名模板
            const outputTemplate = path.join(outputDir, 'frame_%04d.jpg');
            // 构建FFmpeg命令
            const cmd = `"${ffmpeg}" -i "${videoPath}" -ss ${date.toISOString().substr(11, 8)} -vframes 1 "${outputTemplate}"`;
            console.log(cmd)
            try {
                // 使用 promisified exec 执行命令
                await execPromise(cmd);
                // 获取生成的图片列表
                const files = await fs.promises.readdir(outputDir);
                const imageFiles = files
                    .filter(file => file.match(/^frame_\d{4}\.jpg$/))
                    .sort()
                    .map(file => path.join(outputDir, file));
                return imageFiles;
            } catch (error) {
                throw new Error(`FFmpeg截取视频帧失败: ${error.stderr}`);
            }
          },
          async checkFaceVideo(videoPath, tmpPath, duration) {
            if (!this.deleteFaceVideo) {
              return false;
            }
            const filename = path.basename(videoPath, path.extname(videoPath));
            const tmpImgDir =  path.join(tmpPath, `${filename}`)
            const imgList = await this.extractFrames(videoPath, tmpImgDir, 1, duration);
            console.log(imgList)
            let hasFace = false
            for (const img of imgList) {
              hasFace = await this.detectFace(img);
              if (hasFace) {
                break;
              }
            }
            if (hasFace) {
              try {
                console.log('删除包含人脸视频' + videoPath)
                await fs.promises.unlink(videoPath);
              } catch (err) {}
            }
            return hasFace;
          },
          async splitVideo(sceneChanges, sucaiPath, width, height, savePath, outputPrefix) {
            const timePoints = [...sceneChanges];
            const results = [];
            let shortResults = [];
            let longResults = [];
            const tmpImgDir =  path.join(savePath, `tmp`)
           
            for (let i = 0; i < timePoints.length - 1; i++) {
              const startTime = timePoints[i];
              const endTime = timePoints[i + 1];
              const duration = endTime - startTime;

              if (longResults.length >= this.maxSaveCount) {
                break;
              }
              // 跳过过短的片段
              if (duration < this.minSegmentDuration) {
                console.log(`跳过过短片段 ${i + 1}: ${duration.toFixed(2)}s`);
                results.push({ index: i, success: false, skipped: true });
                continue;
              }
              const outputFilePath = path.join(savePath, `${outputPrefix}-${i}.mp4`);
              if (duration < this.minSaveSegmentDuration) {
                shortResults.push({
                  startTime,
                  endTime,
                  duration,
                  path: outputFilePath,
                });
              } else {
                longResults.push({
                  startTime,
                  endTime,
                  duration,
                  path: outputFilePath,
                });
              }
              let scaleWidth = parseInt(1080 * this.cropScale);
              let scaleHeight = parseInt(1080 / width * height * this.cropScale);
              if (scaleHeight < this.height) {
                scaleHeight = parseInt(this.height * this.cropScale);
                scaleWidth = parseInt(scaleHeight / height * width);
              }
              const filter = `-filter_complex "[0:v]scale=${scaleWidth}:${scaleHeight},crop=${this.width}:${this.height}:${(scaleWidth - this.width) / 2}:${this.isTop ? '0' : 'ih-oh'}[v]" -map "[v]"`;
              
              // 修改后的FFmpeg命令，添加裁剪滤镜并重新编码视频
              const command = `"${ffmpeg}" -i "${sucaiPath}" -ss ${startTime} -t ${duration} ${filter} -c:v libx264 -movflags +faststart "${outputFilePath}"`;
              console.log(command)
              console.log(`正在切分片段 ${i + 1}/${timePoints.length - 1}: ${startTime}s - ${endTime}s (${duration.toFixed(2)}s)`);
              // 重试机制
              try {
                await execPromise(command);
                console.log(`片段 ${i + 1} 切分完成: ${outputFilePath}`);
                results.push({ index: i, success: true });
              } catch (error) {
                throw new Error(`片段 ${i + 1} 切分失败: ${error.message}`);
              }
            }
            // 检测人脸并删除
            for (const item of longResults) {
              await this.checkFaceVideo(item.path, tmpImgDir, item.duration)
            }
            
            const successful = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success && !r.skipped).length;
            const skipped = results.filter(r => r.skipped).length;

            if (longResults.length >= this.maxSaveCount) {
              for (const item of shortResults) {
                try {
                  console.log('删除时长不足的视频' + item.path)
                  await fs.promises.unlink(item.path);
                } catch(err) {}
              }
            } else {
              shortResults = shortResults.sort((a, b) => b.duration - a.duration)
              let preDuration = 0;
              let tmpList = [];
              for (const item of shortResults) {
                if (longResults.length >= this.maxSaveCount) {
                  break;
                }
                const hasFace = await this.checkFaceVideo(item.path, tmpImgDir, item.duration)
                if (hasFace) {
                  continue;
                }
                tmpList.push(item)
                preDuration += item.duration;
                if (preDuration >= this.minSaveSegmentDuration) {
                  preDuration = 0;
                  const outputFilePath2 = path.join(savePath, `${outputPrefix}-${Date.now()}.mp4`);
                  let joinFiles = ""
                  let filters = ""
                  for (const index in tmpList) {
                    joinFiles += ` -i "${tmpList[index].path}"`;
                    filters += `[${index}:v]`
                  }
                  const command = `"${ffmpeg}" ${joinFiles} -filter_complex "${filters}concat=n=${tmpList.length}:v=1:a=0[outv]" -map "[outv]" -c:v libx264 "${outputFilePath2}"`
                  if (this.isMerge) {
                    console.log(command)
                    await execPromise(command);
                  }
                  tmpList = [];
                  longResults.push({
                    duration: preDuration,
                    path: outputFilePath2
                  })
                }
              }
              for (const item of shortResults) {
                try {
                  console.log('删除时长不足的文件' + item.path)
                  await fs.promises.unlink(item.path);
                } catch(err) {}
              }
            }
            await this.deleteFolderRecursive(tmpImgDir)
            return { total: timePoints.length - 1, successful, failed, skipped, results };
          },
          async generateQRCode() {
            try {
              // 生成到canvas
              await QRCode.toCanvas(this.$refs.qrCanvas, `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxa5f32aecaf36d407&redirect_uri=http%3A%2F%2Fwang.admin.novel.meeluo.com%2Fwechat_oauth.html%3FdeviceId%3D${this.deviceId}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`, {
                width: 200,
                margin: 2,
                color: {
                  dark: '#000000',  // 二维码点颜色
                  light: '#ffffff'   // 背景颜色
                }
              })
            } catch (err) {
              console.error('生成二维码失败:', err)
              alert('生成二维码失败')
            }
          },
          getTime(format = "YmdHis", time) {
            return getTime(format, time)
          },
          copy() {
            // 模拟 输入框
            var cInput = document.createElement("textarea");
            cInput.value = this.deviceId;
            document.body.appendChild(cInput);
            cInput.select();
            document.execCommand("copy");
            this.alert('设备ID已复制');
            document.body.removeChild(cInput);
          },
          selectDir(target) {
            ipcRenderer.send('showdialog', target)
          },
          alert(msg, showCloseBtn = true) {
            setTimeout(() => {
              this.systemMsg = msg;
              this.showSystemMsg = true;
              this.showCloseBtn = showCloseBtn;
            }, 100)
          },
          async onLoadedMetadata(event, target) {
            target.duration = event.target.duration;
            target.width = event.target.videoWidth;
            target.height = event.target.videoHeight;
            console.log(target.path + '素材长宽：' + target.width + '×' + target.height)
            if (!target.duration) {
              target.duration = await this.getVideoDuration(target.path);
            }
            if (!target.width || !target.height) {
              const { width, height } = await this.getVideoDimensions(target.path);
              target.width = width;
              target.height = height;
              console.log(target.path, target.width, target.height)
            }
            // if (target.width != this.width) {
            //   this.alert('视频素材' + target.path + '宽度不等于' + this.width);
            //   return;
            // }
            // if (target.height < this.height) {
            //   this.alert('视频素材' + target.path + '高度不小于' + this.height);
            //   return;
            // }
          },
          async deleteFolderRecursive(directoryPath) {
            try {
                // 判断路径是否存在且是一个目录
                const isDirectory = await fs.promises.stat(directoryPath).then(stats => stats.isDirectory());
                if (!isDirectory) {
                    throw new Error(`${directoryPath} 不是一个目录`);
                }

                // 读取目录中的所有文件及子目录
                const files = await fs.promises.readdir(directoryPath);

                // 遍历目录中的所有项并递归删除
                for (const file of files) {
                    const filePath = path.join(directoryPath, file);
                    const fileStats = await fs.promises.stat(filePath);

                    if (fileStats.isDirectory()) {
                        // 如果是目录，则递归删除
                        await this.deleteFolderRecursive(filePath);
                    } else {
                        // 如果是文件，则直接删除
                        try {
                          console.log('删除文件' + filePath)
                          await fs.promises.unlink(filePath);
                        } catch(err) {}
                    }
                }

                // 当所有文件及子目录都被删除后，删除当前目录
                await fs.promises.rmdir(directoryPath);
                console.log(`目录 "${directoryPath}" 及其内容已被删除`);
            } catch (err) {
                console.error(`删除目录时出错: ${err}`);
            }
        },
      async faceInit(modelPath) {
        // const Canvas = await ipcRenderer.invoke('get-canvas-Canvas');
        // const Image = await ipcRenderer.invoke('get-canvas-Image');
        // const ImageData = await  ipcRenderer.invoke('get-canvas-ImageData');
        const {Canvas, Image, ImageData} = canvas
        faceapi.env.monkeyPatch({ Canvas, Image, ImageData });
        await faceapi.tf.setBackend('cpu');
        await faceapi.nets.ssdMobilenetv1.loadFromDisk(modelPath);
        await faceapi.nets.faceLandmark68Net.loadFromDisk(modelPath);
        await faceapi.nets.faceRecognitionNet.loadFromDisk(modelPath);
      },
      async detectFace(imagePath) {
        try {
          console.log('正在识别图片是否包含人脸:' + imagePath)
          const fileBuffer = fs.readFileSync(imagePath);
          // const loadImage = await ipcRenderer.invoke('get-canvas-loadImage')
          const imageData = await canvas.loadImage(fileBuffer);
          const options = new faceapi.SsdMobilenetv1Options({
            minConfidence: 0.5,
            maxResults: 1 // 只检测一张人脸（如果只需要判断是否有人脸）
          });
          console.log('识别中...')
          const detections = await faceapi.detectAllFaces(imageData, options);
          console.log(detections.length > 0 ? '有人脸' : '无人脸');
          return detections.length > 0;
          // const detections = await faceapi.detectSingleFace(imageData)
          //   .withFaceLandmarks()
          //   .withFaceDescriptor();
          // console.log(detections ? '有人脸' : '无人脸');
          // return !!detections;
        } catch (err) {
          console.log(err)
          throw err
        }
      },
		  async checkLogin(isClick = false) {
        this.deviceId = await generateCustomId();
        window.localStorage.setItem('secret', this.secret)
        ipcRenderer.send('checkLogin', this.secret, this.deviceId, isClick)
		  },
		  simpleDecrypto(str, xor, hex) {
        const resultList = [];
        hex = hex <= 25 ? hex : hex % 25;
        // 解析出分割字符
        const splitStr = String.fromCharCode(hex + 97);
        // 分割出加密字符串的加密后的每个字符
        const strCharList = str.split(splitStr);
        for (let i = 0; i < strCharList.length; i++) {
          // 将加密后的每个字符转成加密后的ascll码
          let charCode = parseInt(strCharList[i], hex);
          // 异或解密出原字符的ascll码
          charCode = (charCode * 1) ^ xor;
          const strChar = String.fromCharCode(charCode);
          resultList.push(strChar);
        }
        return resultList.join('');
		  },
		  string62to10(numberCode) {
        if (!numberCode) {
          return null;
        }
        try {
          const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
          const radix = chars.length;
          numberCode = String(numberCode);
          const len = numberCode.length;
          let i = 0;
          let originNumber = 0;
          while (i < len) {
          originNumber += Math.pow(radix, i++) * chars.indexOf(numberCode.charAt(len - i) || '0');
          }
          return originNumber;
        } catch (err) {
          return null;
        }
		  },
      boxStyle() {
        return {
            width: `${this.width}px`,
            height: `${this.height}px`,
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          };
      },
      getWidthStyle(percent) {
        return {
          width: `${percent}%`,
        };
      },
      
      async start() {
        // const errList = []
        // for (const target of this.videoList) {
        //   if (target.width / target.height > 3 / 4) {
        //     errList.push('视频素材' + target.path + '宽高比大于3:4，无法裁剪');
        //     continue;
        //   } 
        // }
        // if (errList.length) {
        //   this.alert(errList.join('\n'))
        //   return
        // }
        this.loading = true;
        this.alert('视频处理中...')
        const time = getTime();
        for (const index in this.videoList) {
          const video = this.videoList[index];
          console.log(video.width, video.height)
          if (video.width / video.height > 3 / 4) {
            console.warn('视频素材' + video.path + '宽高比大于3:4，已跳过');
            continue;
          }
          try {
            // 获取视频总时长
            const duration = video.duration;
            // 检测场景变化
            const sceneChanges = await this.detectSceneChanges(video.path, duration);
            console.log('时间点列表:', sceneChanges);
            if (!Array.isArray(sceneChanges) || !sceneChanges.length) {
              console.log('素材' + video.path + '未能识别出分镜');
              continue;
            }
            // 添加视频结束时间点
            sceneChanges.push(duration);
           
            const savePath = this.getRootPath(time);
            // 切分视频
            const splitResults = await this.splitVideo(sceneChanges, video.path, video.width, video.height, savePath, index);
            
            // 输出结果摘要
            console.log('\n===== 切分结果摘要 =====');
            console.log(`总片段数: ${splitResults.total}`);
            console.log(`成功: ${splitResults.successful}`);
            console.log(`失败: ${splitResults.failed}`);
            
            if (splitResults.failed > 0) {
              console.log('\n失败的片段:');
              splitResults.results.forEach((result, i) => {
                if (result.status === 'rejected' || !result.value.success) {
                  console.log(`- 片段 ${i + 1}: ${result.reason || result.value.error}`);
                }
              });
            }
          } catch (error) {
            this.loading = false;
            console.error('处理过程中发生错误:', error.message);
            this.alert(error.message)
             alert('任务处理失败')
            throw error
          }
        }
        this.alert(`任务处理完成`);
        alert('任务处理完成')
        this.loading = false;
      },
      formatMilliseconds(milliseconds) {  
          var seconds = Math.floor((milliseconds / 1000) % 60);  
          var minutes = Math.floor((milliseconds / (1000 * 60)) % 60);  
          var hours = Math.floor((milliseconds / (1000 * 60 * 60)));  
        
          hours = (hours < 10) ? "0" + hours : hours;  
          minutes = (minutes < 10) ? "0" + minutes : minutes;  
          seconds = (seconds < 10) ? "0" + seconds : seconds;  
        
          return hours + ":" + minutes + ":" + seconds + "." + (milliseconds % 1000);  
      },
      htmlColorToAssColor(htmlColor) {  
          // 移除颜色值前的'#'号  
          htmlColor = htmlColor.replace('#', '');  
        
          // 如果颜色值是3位的简写形式，如"#FFF"，则扩展为6位"#FFFFFF"  
          if (htmlColor.length === 3) {  
              htmlColor = htmlColor[0] + htmlColor[0] + htmlColor[1] + htmlColor[1] + htmlColor[2] + htmlColor[2];  
          }  
        
          // 解析RGB通道  
          const r = htmlColor.substr(0, 2);  
          const g = htmlColor.substr(2, 2);  
          const b = htmlColor.substr(4, 2);  
        
          // ASS颜色使用BGRA格式，并且每个通道是两位16进制数，Alpha通道默认为FF（不透明）  
          const assColor = '00'+ b + g + r;
        
          return assColor.toUpperCase();  
      },
      getRootPath(time) {
        const rootPath = path.join(this.videoPath, '素材库' + '-' + time);
        if (!fs.existsSync(rootPath)) {
          fs.mkdirSync(rootPath)
        }
        return rootPath;
      },
      getTmpPath(p, i) {
        const tmpPath = p + '/' + i;
        if (!fs.existsSync(tmpPath)) {
          fs.mkdirSync(tmpPath)
        }
        return tmpPath;
      },
      async fnSelectVideos() {
        this.recording = true;
        this.genNum = 0;
        this.videoPath = "";
        var fileList = document.getElementById("videos").files;
        var list = [];
        // 使用Promise.all并行处理所有文件
        Promise.all(Array.from(fileList).map(async (file, i) => {
          const p = file.path.replace(' ', '\ ');
          const target = {
            path: p,
          };
          // 第一个文件设置视频路径
          if (i === 0) {
            this.videoPath = path.join(p, '../');
          }
          // 并行获取视频时长和尺寸
          const [duration, dimensions] = await Promise.all([
            this.getVideoDuration(p),
            this.getVideoDimensions(p)
          ]);
          
          // 赋值结果
          target.duration = duration;
          target.width = dimensions.width;
          target.height = dimensions.height;
          
          console.log(`${target.path} 素材长宽：${target.width}×${target.height}`);
          return target;
        })).then(results => {
          // 所有文件处理完成后赋值
          this.videoList = results;
          this.recording = false;
        }).catch(error => {
          this.recording = false;
          this.alert('处理视频时出错:', error)
        });
      },
    },
  });
    </script>
  </body>
</html>
