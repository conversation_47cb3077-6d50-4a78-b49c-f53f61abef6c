"""
视频处理核心模块
转换原JavaScript的视频分割、场景检测、FFmpeg调用等功能
"""
import os
import re
import subprocess
import json
import shutil
import tempfile
from typing import List, Tuple, Dict, Optional, Callable
from pathlib import Path
import threading
import time


class VideoProcessor:
    """视频处理器"""
    
    def __init__(self, ffmpeg_path: str = "ffmpeg", ffprobe_path: str = "ffprobe"):
        """
        初始化视频处理器
        
        Args:
            ffmpeg_path: FFmpeg可执行文件路径
            ffprobe_path: FFprobe可执行文件路径
        """
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
        self.progress_callback: Optional[Callable[[float, str], None]] = None
        self.cancel_flag = threading.Event()
        
        # 验证FFmpeg是否可用
        self._verify_ffmpeg()
    
    def _verify_ffmpeg(self):
        """验证FFmpeg是否可用"""
        try:
            subprocess.run([self.ffmpeg_path, "-version"], 
                         capture_output=True, check=True, timeout=10)
            subprocess.run([self.ffprobe_path, "-version"], 
                         capture_output=True, check=True, timeout=10)
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            raise RuntimeError("FFmpeg或FFprobe不可用，请确保已正确安装并添加到PATH环境变量")
    
    def set_progress_callback(self, callback: Callable[[float, str], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def cancel_processing(self):
        """取消处理"""
        self.cancel_flag.set()
    
    def _update_progress(self, progress: float, message: str):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(progress, message)
    
    def get_video_info(self, video_path: str) -> Dict:
        """
        获取视频信息
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            包含视频信息的字典
        """
        try:
            cmd = [
                self.ffprobe_path,
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30,
                                   encoding='utf-8', errors='ignore')
            if result.returncode != 0:
                raise RuntimeError(f"获取视频信息失败: {result.stderr}")
            
            info = json.loads(result.stdout)
            
            # 提取视频流信息
            video_stream = None
            for stream in info.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if not video_stream:
                raise RuntimeError("未找到视频流")
            
            duration = float(info.get('format', {}).get('duration', 0))
            width = int(video_stream.get('width', 0))
            height = int(video_stream.get('height', 0))
            
            return {
                'duration': duration,
                'width': width,
                'height': height,
                'codec': video_stream.get('codec_name', ''),
                'fps': eval(video_stream.get('r_frame_rate', '0/1')),
                'bitrate': int(info.get('format', {}).get('bit_rate', 0))
            }
            
        except Exception as e:
            raise RuntimeError(f"获取视频信息失败: {str(e)}")
    
    def detect_scene_changes(self, video_path: str, threshold: float = 0.1) -> List[float]:
        """
        检测场景变化时间点

        Args:
            video_path: 视频文件路径
            threshold: 场景变化阈值 (用户输入，会被转换为FFmpeg合适的值)

        Returns:
            场景变化时间点列表
        """
        try:
            self._update_progress(0, "开始检测场景变化...")

            # 按原始逻辑：用户阈值除以10
            ffmpeg_threshold = threshold / 10.0

            print(f"DEBUG: 用户阈值={threshold}, FFmpeg阈值={ffmpeg_threshold} (原始逻辑: threshold/10)")

            # 获取视频信息
            video_info = self.get_video_info(video_path)
            duration = video_info['duration']

            print(f"DEBUG: 视频时长={duration}秒")

            # 构建FFmpeg命令 - 按原始逻辑使用showinfo
            cmd = [
                self.ffmpeg_path,
                "-i", video_path,
                "-filter_complex", f"select='gt(scene,{ffmpeg_threshold})',showinfo",
                "-f", "null", "-"
            ]

            print(f"DEBUG: FFmpeg命令: {' '.join(cmd)}")

            # 执行命令
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                encoding='utf-8',
                errors='ignore'
            )

            time_points = []
            all_output = ""

            # 读取所有输出
            stdout, stderr = process.communicate()
            all_output = stdout + stderr

            print(f"DEBUG: FFmpeg输出长度: {len(all_output)} 字符")
            print(f"DEBUG: FFmpeg输出前500字符: {all_output[:500]}")

            # 按原始逻辑解析时间点 - 只使用pts_time模式
            pattern = r'pts_time:(\d+\.?\d*)'
            matches = re.findall(pattern, all_output)

            print(f"DEBUG: 使用原始模式 {pattern}, 匹配数: {len(matches)}")

            for match in matches:
                time_point = float(match)
                if 0 <= time_point <= duration:
                    time_points.append(time_point)
                    print(f"DEBUG: 找到场景变化点: {time_point:.2f}s")

            # 按原始逻辑处理时间点
            if not time_points:
                print("DEBUG: 未检测到场景变化")
                if duration < 3:
                    print("DEBUG: 视频太短，返回空列表")
                    return []
                else:
                    print("DEBUG: 使用默认分割点 (duration-3)")
                    time_points = [duration - 3]

            # 去重并排序
            time_points = sorted(list(set(time_points)))

            # 按原始逻辑：添加开始时间点（0秒）
            if len(time_points) > 0 and time_points[0] != 0:
                time_points.insert(0, 0)

            # 注意：原始逻辑中，结束时间点是在主程序中添加的，不是在这里

            print(f"DEBUG: 最终时间点: {time_points}")

            self._update_progress(30, f"检测到 {len(time_points) - 1} 个场景变化")
            return time_points

        except Exception as e:
            print(f"DEBUG: 场景检测异常: {str(e)}")
            raise RuntimeError(f"场景检测失败: {str(e)}")
    
    def split_video_by_scenes(self, video_path: str, time_points: List[float], 
                            output_dir: str, settings: Dict) -> List[Dict]:
        """
        根据场景时间点分割视频
        
        Args:
            video_path: 输入视频路径
            time_points: 场景时间点列表
            output_dir: 输出目录
            settings: 分割设置
            
        Returns:
            分割结果列表
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            video_info = self.get_video_info(video_path)
            video_name = Path(video_path).stem
            
            results = []
            short_segments = []
            long_segments = []
            
            total_segments = len(time_points) - 1
            
            for i in range(total_segments):
                if self.cancel_flag.is_set():
                    raise RuntimeError("用户取消操作")
                
                if len(long_segments) >= settings.get('max_save_count', 20):
                    break
                
                start_time = time_points[i]
                end_time = time_points[i + 1]
                duration = end_time - start_time
                
                progress = (i / total_segments) * 70 + 30  # 30-100%
                self._update_progress(progress, f"处理片段 {i+1}/{total_segments}")
                
                # 跳过过短的片段
                if duration < settings.get('min_segment_duration', 1):
                    results.append({
                        'index': i,
                        'success': False,
                        'skipped': True,
                        'reason': f'片段过短: {duration:.2f}s'
                    })
                    continue
                
                output_path = os.path.join(output_dir, f"{video_name}-{i}.mp4")
                
                # 构建FFmpeg命令
                success = self._split_single_segment(
                    video_path, start_time, duration, output_path, 
                    video_info, settings
                )
                
                if success:
                    segment_info = {
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': duration,
                        'path': output_path
                    }
                    
                    if duration < settings.get('min_save_segment_duration', 3):
                        short_segments.append(segment_info)
                    else:
                        long_segments.append(segment_info)
                    
                    results.append({'index': i, 'success': True})
                else:
                    results.append({
                        'index': i,
                        'success': False,
                        'skipped': False,
                        'reason': '分割失败'
                    })
            
            # 处理短片段合并
            if settings.get('is_merge', True) and short_segments:
                self._merge_short_segments(short_segments, long_segments, 
                                         output_dir, video_name, settings)
            
            # 清理短片段文件
            for segment in short_segments:
                try:
                    if os.path.exists(segment['path']):
                        os.remove(segment['path'])
                except:
                    pass
            
            self._update_progress(100, "视频分割完成")
            
            return {
                'total': total_segments,
                'successful': len([r for r in results if r['success']]),
                'failed': len([r for r in results if not r['success'] and not r.get('skipped', False)]),
                'skipped': len([r for r in results if r.get('skipped', False)]),
                'results': results,
                'output_segments': long_segments
            }
            
        except Exception as e:
            raise RuntimeError(f"视频分割失败: {str(e)}")
    
    def _split_single_segment(self, input_path: str, start_time: float,
                            duration: float, output_path: str,
                            video_info: Dict, settings: Dict) -> bool:
        """分割单个片段"""
        try:
            print(f"DEBUG: 分割片段 {start_time:.2f}s-{start_time+duration:.2f}s 到 {output_path}")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 简化的FFmpeg命令 - 先不做复杂的缩放和裁剪
            cmd = [
                self.ffmpeg_path,
                "-i", input_path,
                "-ss", str(start_time),
                "-t", str(duration),
                "-c:v", "libx264",
                "-c:a", "aac",
                "-movflags", "+faststart",
                "-y",  # 覆盖输出文件
                output_path
            ]

            print(f"DEBUG: FFmpeg命令: {' '.join(cmd)}")

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300,
                                   encoding='utf-8', errors='ignore')

            print(f"DEBUG: FFmpeg返回码: {result.returncode}")
            if result.returncode != 0:
                print(f"DEBUG: FFmpeg错误: {result.stderr}")
            else:
                # 检查文件是否真的创建了
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"DEBUG: 文件创建成功: {output_path} (大小: {file_size} 字节)")
                else:
                    print(f"DEBUG: 文件未创建: {output_path}")
                    return False

            return result.returncode == 0

        except Exception as e:
            print(f"DEBUG: 分割异常: {str(e)}")
            return False

    def split_video_original_logic(self, time_points: List[float], video_path: str,
                                 width: int, height: int, save_path: str,
                                 output_prefix: str, settings: Dict) -> Dict:
        """
        按照原始HTML逻辑分割视频 - 完全对应JS的splitVideo方法
        """
        try:
            print(f"DEBUG: 开始按原始JS逻辑分割视频")
            print(f"DEBUG: 时间点: {time_points}")
            print(f"DEBUG: 输出路径: {save_path}")

            # 按原始JS逻辑初始化
            results = []
            short_results = []  # 短片段 (< minSaveSegmentDuration)
            long_results = []   # 长片段 (>= minSaveSegmentDuration)

            # 创建临时目录
            tmp_path = os.path.join(save_path, 'tmp')
            os.makedirs(tmp_path, exist_ok=True)

            # 获取参数 (对应JS变量)
            min_segment_duration = settings.get('min_segment_duration', 1)  # this.minSegmentDuration
            min_save_segment_duration = settings.get('min_save_segment_duration', 3)  # this.minSaveSegmentDuration
            max_save_count = settings.get('max_save_count', 20)  # this.maxSaveCount
            crop_scale = settings.get('crop_scale', 1.0)  # this.cropScale
            video_width = settings.get('video_width', 1080)  # this.width
            video_height = settings.get('video_height', 1440)  # this.height
            is_top = settings.get('is_top', True)  # this.isTop
            is_merge = settings.get('is_merge', True)  # this.isMerge
            delete_face_video = settings.get('delete_face_video', False)  # this.deleteFaceVideo

            total_segments = len(time_points) - 1
            print(f"DEBUG: 参数 - 总片段:{total_segments}, 最短时长:{min_segment_duration}s, 合成时长:{min_save_segment_duration}s, 最大数量:{max_save_count}")

            # 第一阶段：分割所有片段 (对应JS的for循环)
            for i in range(total_segments):
                if self.cancel_flag.is_set():
                    break

                # 🔑 关键：当长片段数量达到maxSaveCount时停止 (对应JS第819行)
                if len(long_results) >= max_save_count:
                    print(f"DEBUG: 长片段数量({len(long_results)}) >= 最大数量({max_save_count})，停止处理")
                    break

                start_time = time_points[i]
                end_time = time_points[i + 1]
                duration = end_time - start_time

                print(f"DEBUG: 处理片段 {i+1}/{total_segments}: {start_time:.2f}s - {end_time:.2f}s (时长: {duration:.2f}s)")

                # 跳过过短的片段 (对应JS: if (duration < this.minSegmentDuration) continue;)
                if duration < min_segment_duration:
                    print(f"DEBUG: 跳过过短片段: {duration:.2f}s < {min_segment_duration}s")
                    results.append({'index': i, 'success': False, 'skipped': True, 'reason': '片段过短'})
                    continue

                # 生成输出文件名 (对应JS: const outputFilePath = path.join(savePath, `${outputPrefix}-${i}.mp4`);)
                output_filename = f"{output_prefix}-{i}.mp4"
                output_path = os.path.join(save_path, output_filename)

                # 先分类片段 (对应JS的分类逻辑)
                segment_info = {
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'path': output_path,
                    'index': i
                }

                # 按时长分类 (对应JS的分类逻辑)
                if duration < min_save_segment_duration:
                    short_results.append(segment_info)
                    print(f"DEBUG: 预分类为短片段: {duration:.2f}s < {min_save_segment_duration}s")
                else:
                    long_results.append(segment_info)
                    print(f"DEBUG: 预分类为长片段: {duration:.2f}s >= {min_save_segment_duration}s")

                # 计算缩放参数 (对应JS的缩放逻辑)
                scale_width = int(video_width * crop_scale)
                scale_height = int(scale_width / width * height)

                if scale_height < video_height:
                    scale_height = int(video_height * crop_scale)
                    scale_width = int(scale_height / height * width)

                # 构建FFmpeg命令 (对应JS的filter逻辑)
                crop_x = (scale_width - video_width) // 2
                crop_y = "0" if is_top else "ih-oh"

                filter_complex = f"[0:v]scale={scale_width}:{scale_height},crop={video_width}:{video_height}:{crop_x}:{crop_y}[v]"

                cmd = [
                    self.ffmpeg_path,
                    "-i", video_path,
                    "-ss", str(start_time),
                    "-t", str(duration),
                    "-filter_complex", filter_complex,
                    "-map", "[v]",
                    "-c:v", "libx264",
                    "-movflags", "+faststart",
                    "-y",
                    output_path
                ]

                print(f"DEBUG: FFmpeg命令: {' '.join(cmd)}")

                # 执行分割
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=300,
                                           encoding='utf-8', errors='ignore')

                    if result.returncode == 0 and os.path.exists(output_path):
                        file_size = os.path.getsize(output_path)
                        print(f"DEBUG: 片段 {i+1} 切分完成: {output_path} (大小: {file_size} 字节)")

                        results.append({'index': i, 'success': True})
                    else:
                        print(f"DEBUG: 片段 {i+1} 切分失败: {result.stderr}")
                        # 分割失败，从分类列表中移除
                        if segment_info in short_results:
                            short_results.remove(segment_info)
                        if segment_info in long_results:
                            long_results.remove(segment_info)
                        results.append({'index': i, 'success': False, 'error': result.stderr})

                except Exception as e:
                    print(f"DEBUG: 片段 {i+1} 处理异常: {str(e)}")
                    # 处理异常，从分类列表中移除
                    if segment_info in short_results:
                        short_results.remove(segment_info)
                    if segment_info in long_results:
                        long_results.remove(segment_info)
                    results.append({'index': i, 'success': False, 'error': str(e)})

                # 更新进度
                progress = 30 + (i / total_segments) * 40  # 30-70%
                self._update_progress(progress, f"分割片段 {i+1}/{total_segments}")

            print(f"DEBUG: 分割完成 - 长片段:{len(long_results)}个, 短片段:{len(short_results)}个")

            # 第二阶段：人脸检测 (对应JS: for (const item of longResults))
            if delete_face_video and long_results:
                print(f"DEBUG: 开始对 {len(long_results)} 个长片段进行人脸检测")
                self._update_progress(70, "进行人脸检测...")
                # TODO: 实现人脸检测逻辑
                # await this.checkFaceVideo(item.path, tmpImgDir, item.duration)

            # 第三阶段：处理短片段 (对应JS的短片段处理逻辑)
            if len(long_results) >= max_save_count:
                # 删除所有短片段 (对应JS: if (longResults.length >= this.maxSaveCount))
                print(f"DEBUG: 长片段数量({len(long_results)}) >= 最大数量({max_save_count})，删除所有短片段")
                for item in short_results:
                    try:
                        if os.path.exists(item['path']):
                            os.remove(item['path'])
                            print(f"DEBUG: 删除短片段: {item['path']}")
                    except Exception as e:
                        print(f"DEBUG: 删除短片段失败: {e}")
                short_results = []  # 清空短片段列表
            elif is_merge and short_results:
                # 合并短片段 (对应JS的合并逻辑)
                print(f"DEBUG: 开始合并 {len(short_results)} 个短片段")
                self._update_progress(80, "合并短片段...")

                # 实现完整的短片段合并逻辑
                merged_segments = self._merge_short_segments_complete(
                    short_results, long_results, save_path, output_prefix,
                    min_save_segment_duration, max_save_count, tmp_path, delete_face_video
                )

                # 将合并后的片段添加到长片段列表
                long_results.extend(merged_segments)

                # 删除所有原始短片段文件
                for item in short_results:
                    try:
                        if os.path.exists(item['path']):
                            os.remove(item['path'])
                            print(f"DEBUG: 删除已合并的短片段: {item['path']}")
                    except Exception as e:
                        print(f"DEBUG: 删除短片段失败: {e}")
                short_results = []  # 清空短片段列表

            successful_count = len([r for r in results if r.get('success', False)])
            failed_count = len([r for r in results if not r.get('success', False) and not r.get('skipped', False)])
            skipped_count = len([r for r in results if r.get('skipped', False)])

            print(f"DEBUG: 最终结果 - 成功:{successful_count}, 失败:{failed_count}, 跳过:{skipped_count}")
            print(f"DEBUG: 最终输出 - 长片段:{len(long_results)}个, 短片段:{len(short_results)}个")

            self._update_progress(100, "分割完成")

            # 清理临时文件夹
            try:
                if os.path.exists(tmp_path):
                    import shutil
                    shutil.rmtree(tmp_path)
                    print(f"DEBUG: 已清理临时文件夹: {tmp_path}")
            except Exception as e:
                print(f"DEBUG: 清理临时文件夹失败: {e}")

            return {
                'total': len(results),
                'successful': successful_count,
                'failed': failed_count,
                'skipped': skipped_count,
                'results': results,
                'short_segments': short_results,
                'long_segments': long_results
            }

        except Exception as e:
            print(f"DEBUG: 分割过程异常: {str(e)}")
            # 即使出错也要尝试清理临时文件夹
            try:
                if os.path.exists(tmp_path):
                    import shutil
                    shutil.rmtree(tmp_path)
                    print(f"DEBUG: 异常情况下已清理临时文件夹: {tmp_path}")
            except:
                pass
            raise RuntimeError(f"视频分割失败: {str(e)}")
    
    def _merge_short_segments_complete(self, short_results, long_results, save_path,
                                     output_prefix, min_save_segment_duration, max_save_count,
                                     tmp_path, delete_face_video):
        """完整的短片段合并逻辑 (对应原始JS逻辑)"""
        merged_segments = []

        try:
            # 按时长排序 (对应JS: shortResults = shortResults.sort((a, b) => b.duration - a.duration))
            short_results_sorted = sorted(short_results, key=lambda x: x['duration'], reverse=True)

            pre_duration = 0
            tmp_list = []

            for item in short_results_sorted:
                if len(long_results) + len(merged_segments) >= max_save_count:
                    break

                # TODO: 人脸检测 (对应JS: const hasFace = await this.checkFaceVideo(...))
                # has_face = await self.check_face_video(item['path'], tmp_path, item['duration'])
                # if has_face: continue

                tmp_list.append(item)
                pre_duration += item['duration']

                # 当累计时长达到要求时进行合并 (对应JS: if (preDuration >= this.minSaveSegmentDuration))
                if pre_duration >= min_save_segment_duration:
                    # 生成合并后的文件名
                    output_filename = f"{output_prefix}-merged-{len(merged_segments)}.mp4"
                    output_path = os.path.join(save_path, output_filename)

                    # 合并视频文件 (对应JS的FFmpeg concat命令)
                    if self._merge_video_files_ffmpeg([item['path'] for item in tmp_list], output_path):
                        merged_segments.append({
                            'duration': pre_duration,
                            'path': output_path,
                            'start_time': tmp_list[0]['start_time'],
                            'end_time': tmp_list[-1]['end_time']
                        })
                        print(f"DEBUG: 成功合并 {len(tmp_list)} 个短片段，总时长: {pre_duration:.2f}s -> {output_filename}")

                    # 重置累计变量
                    tmp_list = []
                    pre_duration = 0

            print(f"DEBUG: 短片段合并完成，生成 {len(merged_segments)} 个合并片段")
            return merged_segments

        except Exception as e:
            print(f"DEBUG: 短片段合并失败: {e}")
            return []
    
    def _merge_video_files(self, input_files: List[str], output_path: str) -> bool:
        """合并多个视频文件"""
        try:
            # 创建临时文件列表
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                for file_path in input_files:
                    f.write(f"file '{file_path}'\n")
                list_file = f.name
            
            try:
                cmd = [
                    self.ffmpeg_path,
                    "-f", "concat",
                    "-safe", "0",
                    "-i", list_file,
                    "-c", "copy",
                    "-y",
                    output_path
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300,
                                       encoding='utf-8', errors='ignore')
                return result.returncode == 0
                
            finally:
                os.unlink(list_file)
                
        except Exception:
            return False

    def _merge_video_files_ffmpeg(self, input_files: List[str], output_path: str) -> bool:
        """使用FFmpeg合并多个视频文件 (对应原始JS的concat逻辑)"""
        try:
            if not input_files:
                return False

            if len(input_files) == 1:
                # 只有一个文件，直接复制
                import shutil
                shutil.copy2(input_files[0], output_path)
                return True

            # 构建FFmpeg命令 (对应JS的filter_complex concat)
            # 原始JS: `"${ffmpeg}" ${joinFiles} -filter_complex "${filters}concat=n=${tmpList.length}:v=1:a=0[outv]" -map "[outv]" -c:v libx264 "${outputFilePath2}"`

            cmd = [self.ffmpeg_path]

            # 添加输入文件
            for file_path in input_files:
                cmd.extend(["-i", file_path])

            # 构建filter_complex
            filters = ""
            for i in range(len(input_files)):
                filters += f"[{i}:v]"

            filter_complex = f"{filters}concat=n={len(input_files)}:v=1:a=0[outv]"

            cmd.extend([
                "-filter_complex", filter_complex,
                "-map", "[outv]",
                "-c:v", "libx264",
                "-y",  # 覆盖输出文件
                output_path
            ])

            print(f"DEBUG: FFmpeg合并命令: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300,
                                   encoding='utf-8', errors='ignore')

            if result.returncode == 0 and os.path.exists(output_path):
                print(f"DEBUG: 合并成功: {output_path}")
                return True
            else:
                print(f"DEBUG: 合并失败，返回码: {result.returncode}")
                print(f"DEBUG: stderr: {result.stderr}")
                return False

        except Exception as e:
            print(f"DEBUG: 合并视频失败: {e}")
            return False


# 使用示例
if __name__ == "__main__":
    processor = VideoProcessor()
    
    def progress_callback(progress: float, message: str):
        print(f"进度: {progress:.1f}% - {message}")
    
    processor.set_progress_callback(progress_callback)
    
    # 示例：处理视频
    video_path = "test_video.mp4"
    output_dir = "output"
    
    if os.path.exists(video_path):
        try:
            # 获取视频信息
            info = processor.get_video_info(video_path)
            print(f"视频信息: {info}")
            
            # 检测场景变化
            time_points = processor.detect_scene_changes(video_path, threshold=0.1)
            print(f"场景时间点: {time_points}")
            
            # 分割视频
            settings = {
                'min_segment_duration': 1,
                'min_save_segment_duration': 3,
                'max_save_count': 20,
                'crop_scale': 1.0,
                'video_width': 1080,
                'video_height': 1440,
                'is_top': True,
                'is_merge': True
            }
            
            results = processor.split_video_by_scenes(video_path, time_points, output_dir, settings)
            print(f"分割结果: {results}")
            
        except Exception as e:
            print(f"处理失败: {e}")
    else:
        print("测试视频文件不存在")
