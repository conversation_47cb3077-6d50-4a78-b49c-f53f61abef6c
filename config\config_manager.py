"""
配置管理模块
实现配置文件管理、用户设置保存等功能
"""
import json
import os
import configparser
from typing import Dict, Any, Optional, List
from pathlib import Path
import shutil


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置目录路径，如果不提供则使用默认路径
        """
        if config_dir is None:
            # 使用用户主目录下的应用配置目录
            home_dir = Path.home()
            self.config_dir = home_dir / ".shanlingmao_video_splitter"
        else:
            self.config_dir = Path(config_dir)
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.config_file = self.config_dir / "config.json"
        self.user_settings_file = self.config_dir / "user_settings.json"
        self.auth_cache_file = self.config_dir / "auth_cache.json"
        
        # 默认配置
        self.default_config = {
            "app": {
                "name": "闪灵猫-分割",
                "version": "2.0.4",
                "author": "Python版本",
                "window_width": 800,
                "window_height": 600,
                "theme": "default"
            },
            "auth": {
                "single_code": "O3A981BE3BBC4CB7F2D7A7D33ECB296E",
                "version": "1.0",
                "auto_login": True,
                "remember_device": True
            },
            "video": {
                "ffmpeg_path": "ffmpeg",
                "ffprobe_path": "ffprobe",
                "default_output_dir": "",
                "temp_dir": ""
            },
            "processing": {
                "threshold": 1.0,
                "min_segment_duration": 1,
                "min_save_segment_duration": 3,
                "max_save_count": 20,
                "crop_scale": 1.0,
                "video_width": 1080,
                "video_height": 1440,
                "is_top": True,
                "is_merge": True,
                "delete_face_video": False
            },
            "ui": {
                "last_video_dir": "",
                "last_output_dir": "",
                "show_advanced_settings": False,
                "auto_save_settings": True
            }
        }
        
        # 用户设置
        self.user_settings = {}
        
        # 加载配置
        self.load_config()
        self.load_user_settings()
    
    def load_config(self):
        """加载主配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并默认配置和加载的配置
                    self.config = self._merge_config(self.default_config, loaded_config)
            else:
                self.config = self.default_config.copy()
                self.save_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.config = self.default_config.copy()
    
    def save_config(self):
        """保存主配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def load_user_settings(self):
        """加载用户设置"""
        try:
            if self.user_settings_file.exists():
                with open(self.user_settings_file, 'r', encoding='utf-8') as f:
                    self.user_settings = json.load(f)
            else:
                self.user_settings = {}
        except Exception as e:
            print(f"加载用户设置失败: {e}")
            self.user_settings = {}
    
    def save_user_settings(self):
        """保存用户设置"""
        try:
            with open(self.user_settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存用户设置失败: {e}")
    
    def _merge_config(self, default: Dict, loaded: Dict) -> Dict:
        """合并配置字典"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 "app.name"
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self.config
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any, save: bool = True):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
            save: 是否立即保存到文件
        """
        try:
            keys = key.split('.')
            config = self.config
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            config[keys[-1]] = value
            
            if save:
                self.save_config()
        except Exception as e:
            print(f"设置配置值失败: {e}")
    
    def get_user_setting(self, key: str, default: Any = None) -> Any:
        """获取用户设置"""
        return self.user_settings.get(key, default)
    
    def set_user_setting(self, key: str, value: Any, save: bool = True):
        """设置用户设置"""
        self.user_settings[key] = value
        if save:
            self.save_user_settings()
    
    def get_processing_settings(self) -> Dict[str, Any]:
        """获取视频处理设置"""
        return self.get('processing', {})
    
    def set_processing_settings(self, settings: Dict[str, Any], save: bool = True):
        """设置视频处理设置"""
        current_settings = self.get('processing', {})
        current_settings.update(settings)
        self.set('processing', current_settings, save)
    
    def get_auth_settings(self) -> Dict[str, Any]:
        """获取认证设置"""
        return self.get('auth', {})
    
    def set_auth_settings(self, settings: Dict[str, Any], save: bool = True):
        """设置认证设置"""
        current_settings = self.get('auth', {})
        current_settings.update(settings)
        self.set('auth', current_settings, save)
    
    def get_ui_settings(self) -> Dict[str, Any]:
        """获取UI设置"""
        return self.get('ui', {})
    
    def set_ui_settings(self, settings: Dict[str, Any], save: bool = True):
        """设置UI设置"""
        current_settings = self.get('ui', {})
        current_settings.update(settings)
        self.set('ui', current_settings, save)
    
    def load_auth_cache(self) -> Dict[str, Any]:
        """加载认证缓存"""
        try:
            if self.auth_cache_file.exists():
                with open(self.auth_cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载认证缓存失败: {e}")
            return {}
    
    def save_auth_cache(self, cache_data: Dict[str, Any]):
        """保存认证缓存"""
        try:
            with open(self.auth_cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存认证缓存失败: {e}")
    
    def clear_auth_cache(self):
        """清除认证缓存"""
        try:
            if self.auth_cache_file.exists():
                self.auth_cache_file.unlink()
        except Exception as e:
            print(f"清除认证缓存失败: {e}")
    
    def export_config(self, export_path: str) -> bool:
        """
        导出配置到指定路径
        
        Args:
            export_path: 导出文件路径
            
        Returns:
            是否成功
        """
        try:
            export_data = {
                "config": self.config,
                "user_settings": self.user_settings,
                "export_time": str(Path().cwd()),
                "version": self.get('app.version', '1.0.0')
            }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_path: str) -> bool:
        """
        从指定路径导入配置
        
        Args:
            import_path: 导入文件路径
            
        Returns:
            是否成功
        """
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            if 'config' in import_data:
                self.config = self._merge_config(self.default_config, import_data['config'])
                self.save_config()
            
            if 'user_settings' in import_data:
                self.user_settings = import_data['user_settings']
                self.save_user_settings()
            
            return True
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def reset_to_default(self, section: Optional[str] = None):
        """
        重置配置到默认值
        
        Args:
            section: 要重置的配置节，如果为None则重置全部
        """
        if section is None:
            self.config = self.default_config.copy()
        else:
            if section in self.default_config:
                self.config[section] = self.default_config[section].copy()
        
        self.save_config()
    
    def backup_config(self) -> bool:
        """备份当前配置"""
        try:
            backup_dir = self.config_dir / "backups"
            backup_dir.mkdir(exist_ok=True)
            
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"config_backup_{timestamp}.json"
            
            return self.export_config(str(backup_file))
        except Exception as e:
            print(f"备份配置失败: {e}")
            return False
    
    def get_recent_backups(self, limit: int = 10) -> List[str]:
        """获取最近的备份文件列表"""
        try:
            backup_dir = self.config_dir / "backups"
            if not backup_dir.exists():
                return []
            
            backup_files = []
            for file in backup_dir.glob("config_backup_*.json"):
                backup_files.append(str(file))
            
            # 按修改时间排序
            backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            return backup_files[:limit]
        except Exception as e:
            print(f"获取备份列表失败: {e}")
            return []
    
    def cleanup_old_backups(self, keep_count: int = 5):
        """清理旧的备份文件"""
        try:
            backups = self.get_recent_backups(100)  # 获取所有备份
            if len(backups) > keep_count:
                for backup_file in backups[keep_count:]:
                    try:
                        os.remove(backup_file)
                    except:
                        pass
        except Exception as e:
            print(f"清理备份失败: {e}")


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return config_manager.get(key, default)


def set_config(key: str, value: Any, save: bool = True):
    """设置配置值的便捷函数"""
    config_manager.set(key, value, save)


def get_user_setting(key: str, default: Any = None) -> Any:
    """获取用户设置的便捷函数"""
    return config_manager.get_user_setting(key, default)


def set_user_setting(key: str, value: Any, save: bool = True):
    """设置用户设置的便捷函数"""
    config_manager.set_user_setting(key, value, save)


# 使用示例
if __name__ == "__main__":
    # 创建配置管理器
    cm = ConfigManager()
    
    print("=== 配置管理器测试 ===")
    
    # 获取配置
    app_name = cm.get('app.name')
    print(f"应用名称: {app_name}")
    
    # 设置配置
    cm.set('app.window_width', 1024)
    print(f"窗口宽度: {cm.get('app.window_width')}")
    
    # 处理设置
    processing_settings = cm.get_processing_settings()
    print(f"处理设置: {processing_settings}")
    
    # 用户设置
    cm.set_user_setting('last_used_dir', '/home/<USER>/videos')
    print(f"最后使用目录: {cm.get_user_setting('last_used_dir')}")
    
    # 备份配置
    if cm.backup_config():
        print("配置备份成功")
    
    # 获取备份列表
    backups = cm.get_recent_backups()
    print(f"备份文件: {backups}")
    
    print("配置管理器测试完成")
